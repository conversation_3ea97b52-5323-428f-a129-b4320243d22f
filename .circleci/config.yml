version: 2.1

orbs:
  python: circleci/python@3.2.0

jobs:
  hello-job:
    docker:
      - image: cimg/node:17.2.0 # the primary container, where your job's commands are run
    steps:
      - checkout # check out the code in the project directory
      - run: echo "hello world" # run the `echo` command
      - run: echo "Hi <PERSON>!" # Say hi steve
      - run: echo "Hi <PERSON>!" # Say hi <PERSON>
workflows:
  my-workflow:
    jobs:
      - hello-job
  
      - python/test
